# React Next.js Learn Auth

A comprehensive authentication learning project built with Next.js 15, NextAuth.js, and Prisma. This project demonstrates modern authentication patterns including OAuth providers, passwordless email authentication, and secure session management.

## 🚀 Features

### Authentication Methods
- **OAuth Providers**: Google, GitHub, and Amazon sign-in
- **Passwordless Email**: Magic link authentication via Resend
- **Session Management**: Secure server-side sessions with NextAuth.js
- **Database Integration**: User data persistence with Prisma and SQLite

### Security & UX
- **Middleware Protection**: Route-based authentication guards
- **Responsive Design**: Mobile-first UI with Tailwind CSS
- **Error Boundaries**: Graceful error handling and recovery
- **Loading States**: Smooth authentication flow indicators
- **Provider Icons**: Visual authentication provider identification

## 🛠️ Tech Stack

- **Framework**: [Next.js 15](https://nextjs.org/) with App Router
- **Authentication**: [NextAuth.js v4](https://next-auth.js.org/)
- **Database**: [Prisma](https://prisma.io/) with SQLite
- **Styling**: [Tailwind CSS v4](https://tailwindcss.com/)
- **Email**: [Resend](https://resend.com/) for magic links
- **TypeScript**: Full type safety throughout

## 📋 Prerequisites

- Node.js 18+
- pnpm (recommended) or npm/yarn
- Git

## 🚀 Getting Started

### 1. Clone the Repository
```bash
git clone https://github.com/kevinlonigro/react-nextjs-learn-auth.git
cd react-nextjs-learn-auth
```

### 2. Install Dependencies
```bash
pnpm install
```

### 3. Environment Setup
Create a `.env.local` file in the root directory:

```env
# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here

# Database
DATABASE_URL="file:./dev.db"

# OAuth Providers
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

GITHUB_ID=your-github-client-id
GITHUB_SECRET=your-github-client-secret

AMAZON_CLIENT_ID=your-amazon-client-id
AMAZON_CLIENT_SECRET=your-amazon-client-secret

# Email Provider (Resend)
RESEND_API_KEY=your-resend-api-key
RESEND_DOMAIN=your-domain.com
RESEND_SENDER=<EMAIL>
RESEND_RECIPIENT=<EMAIL>
```

### 4. Database Setup
```bash
pnpm db:push
```

### 5. Run Development Server
```bash
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## 📁 Project Structure

```
app/
├── components/          # Reusable UI components
│   ├── auth/           # Authentication-specific components
│   ├── icons/          # Provider icons and UI icons
│   └── ui/             # General UI components
├── dashboard/          # Protected dashboard pages
├── lib/               # Utility functions and configurations
├── login/             # Authentication pages
└── api/auth/          # NextAuth.js API routes

prisma/
├── schema.prisma      # Database schema
└── dev.db            # SQLite database (development)
```

## 🔧 Available Scripts

- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm start` - Start production server
- `pnpm lint` - Run ESLint
- `pnpm type-check` - Run TypeScript type checking
- `pnpm db:push` - Push database schema changes
- `pnpm db:studio` - Open Prisma Studio

## 🔐 Authentication Flow

1. **Home Page**: Landing page with dashboard access
2. **Login Page**: Multiple authentication options
3. **OAuth Flow**: Redirect-based authentication with providers
4. **Email Magic Links**: Passwordless authentication via email
5. **Dashboard**: Protected user profile and session information
6. **Logout**: Secure session termination

## 🎨 UI Components

- **Responsive Design**: Mobile-first approach with breakpoint-specific layouts
- **Loading States**: Visual feedback during authentication processes
- **Error Handling**: User-friendly error messages and recovery options
- **Provider Branding**: Consistent styling with provider-specific colors and icons

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to [Vercel](https://vercel.com)
3. Configure environment variables in Vercel dashboard
4. Deploy automatically on push to main branch

### Environment Variables for Production
Ensure all environment variables are configured in your deployment platform, especially:
- `NEXTAUTH_URL` (your production domain)
- `NEXTAUTH_SECRET` (generate a secure secret)
- OAuth provider credentials
- Database connection string
- Email service configuration

## 📚 Learning Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [NextAuth.js Documentation](https://next-auth.js.org/)
- [Prisma Documentation](https://prisma.io/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)

## 🤝 Contributing

This is a learning project, but contributions and suggestions are welcome! Please feel free to:
- Open issues for bugs or feature requests
- Submit pull requests for improvements
- Share feedback and learning experiences

## 📄 License

This project is open source and available under the [MIT License](LICENSE).