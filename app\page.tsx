import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center px-4 sm:px-6 lg:px-8 font-[family-name:var(--font-geist-sans)]">
      <main className="text-center">
        {/* Title positioned at top fold */}
        <div className="mb-16 sm:mb-20 lg:mb-24">
          <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold text-foreground tracking-tight">
            React Next.js Learn Auth
          </h1>
        </div>

        {/* Dashboard button with matching styling */}
        <div className="flex justify-center">
          <Link
            href="/dashboard"
            className="rounded-full border border-solid border-black/[.08] dark:border-white/[.145] transition-colors flex items-center justify-center hover:bg-[#f2f2f2] dark:hover:bg-[#1a1a1a] hover:border-transparent font-medium text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 w-auto min-w-[120px]"
          >
            Dashboard
          </Link>
        </div>
      </main>
    </div>
  );
}
